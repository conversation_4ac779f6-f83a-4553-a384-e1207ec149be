<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme;

use App\Contracts\Services\Internal\WarehouseOrderSchemeDetectionServiceContract;
use App\Contracts\Services\Internal\WarehouseOrderSchemeValidationServiceContract;
use App\Contracts\Services\Internal\WarehouseOrderSchemeReportsServiceContract;
use App\Services\Api\Internal\WarehouseOrderScheme\WarehouseOrderSchemeDetectionService;
use App\Services\Api\Internal\WarehouseOrderScheme\ValidationService;
use App\Services\Api\Internal\WarehouseOrderScheme\ReportsService\WarehouseOrderSchemeReportsService;
use Illuminate\Support\ServiceProvider;

class WarehouseOrderSchemeServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->bind(WarehouseOrderSchemeDetectionServiceContract::class, WarehouseOrderSchemeDetectionService::class);
        $this->app->bind(WarehouseOrderSchemeValidationServiceContract::class, ValidationService::class);
        $this->app->bind(WarehouseOrderSchemeReportsServiceContract::class, WarehouseOrderSchemeReportsService::class);
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
