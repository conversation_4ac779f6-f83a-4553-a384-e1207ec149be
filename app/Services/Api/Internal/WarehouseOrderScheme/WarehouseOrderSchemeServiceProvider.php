<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme;

use App\Contracts\Services\Internal\WarehouseOrderSchemeDetectionServiceContract;
use App\Contracts\Services\Internal\WarehouseOrderSchemeValidationServiceContract;
use App\Contracts\Services\Internal\WarehouseOrderSchemeReportsServiceContract;
use App\Contracts\Services\Internal\WarehouseReceiptOrdersServiceContract;
use App\Contracts\Services\Internal\WarehouseIssueOrdersServiceContract;
use App\Contracts\Services\Internal\WarehouseReservationsServiceContract;
use App\Contracts\Services\Internal\WarehouseTransactionServiceContract;
use App\Services\Api\Internal\WarehouseOrderScheme\WarehouseOrderSchemeDetectionService;
use App\Services\Api\Internal\WarehouseOrderScheme\ValidationService;
use App\Services\Api\Internal\WarehouseOrderScheme\ReportsService\WarehouseOrderSchemeReportsService;
use App\Services\Api\Internal\WarehouseOrderScheme\WarehouseReceiptOrdersService\WarehouseReceiptOrdersService;
use App\Services\Api\Internal\WarehouseOrderScheme\WarehouseIssueOrdersService\WarehouseIssueOrdersService;
use App\Services\Api\Internal\WarehouseOrderScheme\WarehouseReservationsService\WarehouseReservationsService;
use App\Services\Api\Internal\WarehouseTransactionService\WarehouseTransactionService;
use Illuminate\Support\ServiceProvider;

class WarehouseOrderSchemeServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->bind(WarehouseOrderSchemeDetectionServiceContract::class, WarehouseOrderSchemeDetectionService::class);
        $this->app->bind(WarehouseOrderSchemeValidationServiceContract::class, ValidationService::class);
        $this->app->bind(WarehouseOrderSchemeReportsServiceContract::class, WarehouseOrderSchemeReportsService::class);
        $this->app->bind(WarehouseReceiptOrdersServiceContract::class, WarehouseReceiptOrdersService::class);
        $this->app->bind(WarehouseIssueOrdersServiceContract::class, WarehouseIssueOrdersService::class);
        $this->app->bind(WarehouseReservationsServiceContract::class, WarehouseReservationsService::class);
        $this->app->bind(WarehouseTransactionServiceContract::class, WarehouseTransactionService::class);
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
