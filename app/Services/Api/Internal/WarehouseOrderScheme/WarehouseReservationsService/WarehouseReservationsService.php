<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme\WarehouseReservationsService;

use App\Contracts\Services\Internal\WarehouseReservationsServiceContract;
use App\Services\Api\Internal\WarehouseOrderScheme\WarehouseReservationsService\Handlers\WarehouseReservationsIndexHandler;
use App\Services\Api\Internal\WarehouseOrderScheme\WarehouseReservationsService\Handlers\WarehouseReservationsCreateHandler;
use App\Services\Api\Internal\WarehouseOrderScheme\WarehouseReservationsService\Handlers\WarehouseReservationsCancelHandler;
use App\Services\Api\Internal\WarehouseOrderScheme\WarehouseReservationsService\Handlers\WarehouseReservationsMarkAsShippedHandler;
use App\Services\Api\Internal\WarehouseOrderScheme\WarehouseReservationsService\Handlers\WarehouseReservationsReleaseExpiredHandler;
use App\Services\Api\Internal\WarehouseOrderScheme\WarehouseReservationsService\Handlers\WarehouseReservationsGetAvailabilityHandler;
use App\DTO\IndexRequestDTO;

readonly class WarehouseReservationsService implements WarehouseReservationsServiceContract
{
    public function __construct(
        private WarehouseReservationsIndexHandler $indexHandler,
        private WarehouseReservationsCreateHandler $createHandler,
        private WarehouseReservationsCancelHandler $cancelHandler,
        private WarehouseReservationsMarkAsShippedHandler $markAsShippedHandler,
        private WarehouseReservationsReleaseExpiredHandler $releaseExpiredHandler,
        private WarehouseReservationsGetAvailabilityHandler $getAvailabilityHandler
    ) {
    }

    public function index(IndexRequestDTO $dto): array
    {
        return $this->indexHandler->run($dto);
    }

    public function create(object $dto): array
    {
        return $this->createHandler->run($dto);
    }

    public function cancel(string $id): bool
    {
        return $this->cancelHandler->run($id);
    }

    public function markAsShipped(array $reservationIds): int
    {
        return $this->markAsShippedHandler->run($reservationIds);
    }

    public function releaseExpired(): int
    {
        return $this->releaseExpiredHandler->run();
    }

    public function getAvailability(string $productId, string $warehouseId, string $date): array
    {
        return $this->getAvailabilityHandler->run($productId, $warehouseId, $date);
    }

    public function getReservationTypes(): array
    {
        return [
            'order' => 'Заказ',
            'production' => 'Производство',
            'transfer' => 'Перемещение',
            'marketing' => 'Маркетинг',
            'quality' => 'Контроль качества'
        ];
    }
}
