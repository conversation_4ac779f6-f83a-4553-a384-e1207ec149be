<?php

namespace App\Http\Controllers\Api\Internal\WarehouseOrderScheme;

use App\Contracts\Services\Internal\WarehouseOrderSchemeValidationServiceContract;
use App\Http\Controllers\Controller;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class WarehouseOrderSchemeValidationController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly WarehouseOrderSchemeValidationServiceContract $service
    ) {
    }

    /**
     * @response 200 {"valid": true, "errors": [], "warnings": []}
     */
    public function validateShipment(Request $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $validated = $request->validate([
                'warehouse_id' => 'required|uuid|exists:warehouses,id',
                'items' => 'required|array|min:1',
                'items.*.product_id' => 'required|uuid|exists:products,id',
                'items.*.quantity' => 'required|integer|min:1',
            ]);

            $result = $this->service->validateShipment(
                $validated['warehouse_id'],
                $validated['items']
            );

            return $this->successResponse($result);
        });
    }
}
