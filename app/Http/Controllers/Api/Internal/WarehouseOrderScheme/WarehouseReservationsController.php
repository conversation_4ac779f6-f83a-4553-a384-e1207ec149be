<?php

namespace App\Http\Controllers\Api\Internal\WarehouseOrderScheme;

use App\Contracts\Services\Internal\WarehouseReservationsServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\WarehouseOrderScheme\WarehouseReservationStoreRequest;
use App\Http\Resources\Api\Internal\WarehouseOrderScheme\WarehouseReservationCollection;
use App\Http\Resources\Api\Internal\WarehouseOrderScheme\WarehouseReservationResource;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class WarehouseReservationsController extends Controller
{
    public function __construct(
        private readonly WarehouseReservationsServiceContract $service
    ) {
    }

    /**
     * @response 200 WarehouseReservationCollection<WarehouseReservationResource>
     */
    public function index(Request $request): WarehouseReservationCollection
    {
        $dto = IndexRequestDTO::fromRequest($request);

        if (isset($dto->filters['order_item_id'])) {
            $reservations = $this->repository->getReservationsByOrderItem($dto->filters['order_item_id']);
        } elseif (isset($dto->filters['warehouse_item_id'])) {
            $reservations = $this->repository->getReservationsByWarehouseItem($dto->filters['warehouse_item_id']);
        } else {
            abort(422, 'Необходимо указать order_item_id или warehouse_item_id');
        }

        return WarehouseReservationCollection::make($reservations);
    }

    /**
     * @response 201 {"id": "uuid", "message": "Резерв создан"}
     */
    public function store(WarehouseReservationStoreRequest $request): JsonResponse
    {
        $data = $request->validated();

        // Валидируем возможность резервирования
        $validation = $this->validationService->validateReservationOperation(
            $data['warehouse_id'],
            [$data]
        );

        if (!$validation['valid']) {
            return response()->json([
                'error' => 'Ошибка валидации резервирования',
                'errors' => $validation['errors']
            ], 422);
        }

        $data['id'] = \Illuminate\Support\Str::uuid()->toString();
        $data['reserved_at'] = now();
        $data['status'] = 'reserved';

        $this->repository->insert($data);

        return response()->json([
            'id' => $data['id'],
            'message' => 'Резерв создан',
            'warnings' => $validation['warnings'] ?? []
        ], 201);
    }

    /**
     * @response 200 {"message": "Резерв отменен"}
     */
    public function cancel(string $id): JsonResponse
    {
        $reservation = $this->repository->getReservationsByOrderItem($id)->first();

        if (!$reservation) {
            abort(404, 'Резерв не найден');
        }

        if ($reservation->status !== 'reserved') {
            return response()->json([
                'error' => 'Можно отменить только активный резерв'
            ], 422);
        }

        // Отменяем резерв
        $this->repository->update($id, [
            'status' => 'cancelled'
        ]);

        return response()->json([
            'message' => 'Резерв отменен'
        ]);
    }

    /**
     * @response 200 {"message": "Резервы отмечены как отгруженные"}
     */
    public function markAsShipped(Request $request): JsonResponse
    {
        $reservationIds = $request->input('reservation_ids', []);

        if (empty($reservationIds)) {
            return response()->json([
                'error' => 'Необходимо указать reservation_ids'
            ], 422);
        }

        $updated = $this->repository->markAsShipped($reservationIds);

        return response()->json([
            'message' => "Резервы отмечены как отгруженные: {$updated} шт."
        ]);
    }

    /**
     * @response 200 {"released": 5, "message": "Истекшие резервы освобождены"}
     */
    public function releaseExpired(): JsonResponse
    {
        $released = $this->repository->releaseExpiredReservations();

        return response()->json([
            'released' => $released,
            'message' => "Истекшие резервы освобождены: {$released} шт."
        ]);
    }

    /**
     * @response 200 {"available": 150, "reserved": 50, "total": 200}
     */
    public function getAvailability(Request $request): JsonResponse
    {
        $productId = $request->input('product_id');
        $warehouseId = $request->input('warehouse_id');
        $date = $request->input('date', now()->toDateString());

        if (!$productId || !$warehouseId) {
            return response()->json([
                'error' => 'Необходимо указать product_id и warehouse_id'
            ], 422);
        }

        $available = $this->repository->getAvailableQuantityForProduct($productId, $warehouseId, $date);
        $reserved = $this->repository->getTotalReservedQuantity($warehouseId . '_' . $productId);

        return response()->json([
            'available' => $available,
            'reserved' => $reserved,
            'total' => $available + $reserved
        ]);
    }

    /**
     * @response 200 {"types": ["order", "production", "transfer", "marketing", "quality"]}
     */
    public function getReservationTypes(): JsonResponse
    {
        return response()->json([
            'types' => [
                'order' => 'Под заказы клиентов',
                'production' => 'Под производственные нужды',
                'transfer' => 'Под перемещения между складами',
                'marketing' => 'Под маркетинговые акции',
                'quality' => 'Под контроль качества'
            ]
        ]);
    }
}
