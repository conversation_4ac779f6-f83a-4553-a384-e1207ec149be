<?php

namespace App\Http\Controllers\Api\Internal\WarehouseOrderScheme;

use App\Contracts\Services\Internal\WarehouseOrderSchemeDetectionServiceContract;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class WarehouseOrderSchemeDetectionController extends Controller
{
    public function __construct(
        private readonly WarehouseOrderSchemeDetectionServiceContract $service
    ) {
    }

    /**
     * @response 200 {"is_active": true, "warehouse_id": "uuid", "operation_type": "shipments", "date": "2024-01-15"}
     */
    public function isActive(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'warehouse_id' => 'required|uuid|exists:warehouses,id',
            'operation_type' => 'required|in:acceptances,shipments',
            'date' => 'nullable|date'
        ]);

        $warehouseId = $validated['warehouse_id'];
        $operationType = $validated['operation_type'];
        $date = $validated['date'] ?? now()->toDateString();

        $isActive = match ($operationType) {
            'acceptances' => $this->service->isOrderSchemeActiveForReceipts($warehouseId, $date),
            'shipments' => $this->service->isOrderSchemeActiveForShipments($warehouseId, $date),
        };

        return response()->json([
            'is_active' => $isActive,
            'warehouse_id' => $warehouseId,
            'operation_type' => $operationType,
            'date' => $date
        ]);
    }
}
