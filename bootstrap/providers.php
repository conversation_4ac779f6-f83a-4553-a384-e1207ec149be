<?php

use App\Modules\AI\Providers\AppServiceProvider;
use App\Modules\Marketplaces\Providers\MarketplacesServiceProvider;
use App\Providers\ScrambleServiceProvider;
use App\Services\Api\Internal\Procurement\Acceptances\AcceptancesService\AcceptancesServiceProvider;
use App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrdersService\CustomerOrdersProvider;
use App\Services\Api\Internal\Sales\Shipments\ShipmentsService\ShipmentsServiceProvider;
use App\Services\Api\Internal\WarehouseOrderScheme\WarehouseOrderSchemeServiceProvider;

return [
    App\Modules\AI\Providers\AppServiceProvider::class,
    App\Modules\AI\Providers\AppRepositoryProvider::class,
    MarketplacesServiceProvider::class,
    App\Providers\AppServiceProvider::class,
    App\Providers\PolicyProvider::class,
    App\Providers\RepositoryServiceProvider::class,
    App\Providers\RateLimiterServiceProvider::class,
    AppServiceProvider::class,
    AcceptancesServiceProvider::class,
    CustomerOrdersProvider::class,
    ShipmentsServiceProvider::class,
    WarehouseOrderSchemeServiceProvider::class,
    ScrambleServiceProvider::class,
];
