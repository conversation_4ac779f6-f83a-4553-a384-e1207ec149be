<?php

use App\Modules\AI\Providers\AppServiceProvider;
use App\Modules\Marketplaces\Providers\MarketplacesServiceProvider;

return [
    App\Modules\AI\Providers\AppServiceProvider::class,
    App\Modules\AI\Providers\AppRepositoryProvider::class,
    MarketplacesServiceProvider::class,
    App\Providers\AppServiceProvider::class,
    App\Providers\PolicyProvider::class,
    App\Providers\RepositoryServiceProvider::class,
    App\Providers\RateLimiterServiceProvider::class,
    AppServiceProvider::class,
    \App\Services\Api\Internal\Procurement\Acceptances\AcceptancesService\AcceptancesServiceProvider::class,
    \App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrdersService\CustomerOrdersProvider::class,
    \App\Services\Api\Internal\Sales\Shipments\ShipmentsService\ShipmentsServiceProvider::class,
    \App\Services\Api\Internal\WarehouseOrderScheme\WarehouseOrderSchemeServiceProvider::class,
    \App\Providers\ScrambleServiceProvider::class,
];
