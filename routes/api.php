<?php

use App\Http\Controllers\Api\Internal\Contractors\ContractController;
use App\Http\Controllers\Api\Internal\Contractors\Contractors\ContractorController;
use App\Http\Controllers\Api\Internal\Contractors\Contractors\ContractorGroupsController;
use App\Http\Controllers\Api\Internal\Documents\RelatedDocumentController;
use App\Http\Controllers\Api\Internal\Excel\ExcelImportController;
use App\Http\Controllers\Api\Internal\Excel\ExportExcelController;
use App\Http\Controllers\Api\Internal\Export\Export1CController;
use App\Http\Controllers\Api\Internal\Files\FilesController;
use App\Http\Controllers\Api\Internal\Goods\Attributes\AttributeController;
use App\Http\Controllers\Api\Internal\Goods\Attributes\AttributeGroupsController;
use App\Http\Controllers\Api\Internal\Goods\Attributes\AttributeValueController;
use App\Http\Controllers\Api\Internal\Goods\Other\BarcodeController;
use App\Http\Controllers\Api\Internal\Goods\Other\BrandController;
use App\Http\Controllers\Api\Internal\Goods\Prices\CabinetPriceController;
use App\Http\Controllers\Api\Internal\Goods\Products\ProductAttributeController;
use App\Http\Controllers\Api\Internal\Goods\Products\ProductCategoryController;
use App\Http\Controllers\Api\Internal\Goods\Products\ProductController;
use App\Http\Controllers\Api\Internal\Goods\Products\ProductGroupsController;
use App\Http\Controllers\Api\Internal\Goods\Products\ProductPackingController;
use App\Http\Controllers\Api\Internal\Goods\Products\ProductSizController;
use App\Http\Controllers\Api\Internal\Goods\Transfers\GoodsTransferController;
use App\Http\Controllers\Api\Internal\Goods\Transfers\GoodsTransferItemController;
use App\Http\Controllers\Api\Internal\Money\FinanceController;
use App\Http\Controllers\Api\Internal\Money\IncomingPayments\IncomingPaymentController;
use App\Http\Controllers\Api\Internal\Money\IncomingPayments\IncomingPaymentItemController;
use App\Http\Controllers\Api\Internal\Money\OutgoingPayments\OutgoingPaymentController;
use App\Http\Controllers\Api\Internal\Money\OutgoingPayments\OutgoingPaymentItemController;
use App\Http\Controllers\Api\Internal\Other\BinController;
use App\Http\Controllers\Api\Internal\Other\DevController;
use App\Http\Controllers\Api\Internal\Other\GroupController;
use App\Http\Controllers\Api\Internal\Other\Status\StatusController;
use App\Http\Controllers\Api\Internal\Other\SuggestController;
use App\Http\Controllers\Api\Internal\Ozon\OzonCredentialsController;
use App\Http\Controllers\Api\Internal\Ozon\OzonV1ReturnsFboFbsListController;
use App\Http\Controllers\Api\Internal\Ozon\OzonV1WarehouseController;
use App\Http\Controllers\Api\Internal\Ozon\OzonV2ReturnsRfbsListController;
use App\Http\Controllers\Api\Internal\Ozon\OzonV3FinanceTransactionListController;
use App\Http\Controllers\Api\Internal\Ozon\OzonV3PostingFbsListController;
use App\Http\Controllers\Api\Internal\Procurement\Acceptances\AcceptanceController;
use App\Http\Controllers\Api\Internal\Procurement\Acceptances\AcceptanceItemController;
use App\Http\Controllers\Api\Internal\Procurement\PackingController;
use App\Http\Controllers\Api\Internal\Procurement\VendorOrders\VendorOrderController;
use App\Http\Controllers\Api\Internal\Procurement\VendorOrders\VendorOrderItemController;
use App\Http\Controllers\Api\Internal\References\CabinetCurrencyController;
use App\Http\Controllers\Api\Internal\References\CountryController;
use App\Http\Controllers\Api\Internal\References\DiscountController;
use App\Http\Controllers\Api\Internal\References\GlobalCurrencyController;
use App\Http\Controllers\Api\Internal\References\LegalEntityController;
use App\Http\Controllers\Api\Internal\References\MeasurementUnits\MeasurementUnitController;
use App\Http\Controllers\Api\Internal\References\MeasurementUnits\MeasurementUnitGroupController;
use App\Http\Controllers\Api\Internal\References\ProfitTaxRateController;
use App\Http\Controllers\Api\Internal\References\SalesChannelController;
use App\Http\Controllers\Api\Internal\References\VatRateController;
use App\Http\Controllers\Api\Internal\Sales\ComissionerReports\IssuedComissionReports\IssuedComissionReportsController;
use App\Http\Controllers\Api\Internal\Sales\ComissionerReports\IssuedComissionReports\IssuedComissionReportsItemsController;
use App\Http\Controllers\Api\Internal\Sales\ComissionerReports\ReceivedComissionReports\ReceivedComissionReportsController;
use App\Http\Controllers\Api\Internal\Sales\ComissionerReports\ReceivedComissionReports\ReceivedComissionReportsRealizedItemsController;
use App\Http\Controllers\Api\Internal\Sales\ComissionerReports\ReceivedComissionReports\ReceivedComissionReportsReturnItemsController;
use App\Http\Controllers\Api\Internal\Sales\CustomerOrders\CustomerOrderItemController;
use App\Http\Controllers\Api\Internal\Sales\CustomerOrders\CustomerOrdersController;
use App\Http\Controllers\Api\Internal\Sales\Shipments\ShipmentController;
use App\Http\Controllers\Api\Internal\Sales\Shipments\ShipmentItemController;
use App\Http\Controllers\Api\Internal\User\BookmarkController;
use App\Http\Controllers\Api\Internal\User\EmailVerificationNotificationController;
use App\Http\Controllers\Api\Internal\User\UserAuthController;
use App\Http\Controllers\Api\Internal\User\UserCabinetController;
use App\Http\Controllers\Api\Internal\User\UserSettingsController;
use App\Http\Controllers\Api\Internal\User\UserViewSettingsController;
use App\Http\Controllers\Api\Internal\User\VerifyEmailController;
use App\Http\Controllers\Api\Internal\WarehouseControllers\Contacts\WarehouseAddressController;
use App\Http\Controllers\Api\Internal\WarehouseControllers\Contacts\WarehousePhoneController;
use App\Http\Controllers\Api\Internal\WarehouseControllers\WarehouseCalendarController;
use App\Http\Controllers\Api\Internal\WarehouseControllers\WarehouseCellController;
use App\Http\Controllers\Api\Internal\WarehouseControllers\WarehouseCellGroupController;
use App\Http\Controllers\Api\Internal\WarehouseControllers\WarehouseCellSizeController;
use App\Http\Controllers\Api\Internal\WarehouseControllers\WarehouseController;
use App\Http\Controllers\Api\Internal\WarehouseControllers\WarehouseGroupController;
use App\Http\Controllers\Api\Internal\WarehouseControllers\WarehouseStorageAreaController;
use App\Http\Controllers\Api\Internal\WarehouseControllers\WarehouseWorkScheduleController;
use App\Http\Controllers\Api\Internal\WarehouseOrderScheme\WarehouseOrderSchemeDetectionController;
use App\Http\Controllers\Api\Internal\WarehouseOrderScheme\WarehouseOrderSchemeReportsController;
use App\Http\Controllers\Api\Internal\WarehouseOrderScheme\WarehouseOrderSchemeValidationController;
use App\Http\Controllers\Api\Internal\WarehouseOrderScheme\WarehouseReceiptOrdersController;
use App\Http\Controllers\Api\Internal\WarehouseOrderScheme\WarehouseIssueOrdersController;
use App\Http\Controllers\Api\Internal\WarehouseOrderScheme\WarehouseReservationsController;
use App\Http\Controllers\Api\Internal\Workspace\Cabinet\CabinetController;
use App\Http\Controllers\Api\Internal\Workspace\Cabinet\CabinetInviteController;
use App\Http\Controllers\Api\Internal\Workspace\Cabinet\CabinetSettingsController;
use App\Http\Controllers\Api\Internal\Workspace\Departments\DepartamentPermissionController;
use App\Http\Controllers\Api\Internal\Workspace\Departments\DepartmentController;
use App\Http\Controllers\Api\Internal\Workspace\Employee\EmployeeController;
use App\Http\Controllers\Api\Internal\Workspace\Employee\EmployeePermissionController;
use App\Http\Controllers\Api\Internal\Workspace\Permissions\PermissionController;
use App\Http\Controllers\Api\Internal\Workspace\Permissions\Roles\RoleController;
use App\Http\Middleware\AuthPermissionMiddleware;
use Illuminate\Http\Request;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

require base_path('/app/Modules/AI/routes/api.php');

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

Route::get('/dev', [DevController::class, 'fill']);

Route::post('register', [UserAuthController::class, 'register']);
Route::post('login', [UserAuthController::class, 'login']);
Route::post('email/verification-notification', [EmailVerificationNotificationController::class, 'store'])
    ->middleware('throttle:6,1')
    ->middleware('auth:sanctum'); //Отправка кода

Route::post('verify-email', VerifyEmailController::class)
    ->middleware(['throttle:6,1'])
    ->middleware('auth:sanctum'); //Отправка кода


// Внутреннее API для Frontend. В будущем, возможно, перейдет в версионирование
Route::group([
    'middleware' => ['auth:sanctum', 'verified'],
    'prefix' => 'internal'
    ], static function () {

        Route::group([
            'middleware' => [AuthPermissionMiddleware::class],
        ], static function () {
            require base_path('/app/Modules/Marketplaces/routes/api.php');

            // == Contractor Groups ==
            Route::delete('/contractor_groups/bulk-delete', [ContractorGroupsController::class, 'bulkDelete']);

            Route::get('/contractor_groups', [ContractorGroupsController::class, 'index']);
            Route::post('/contractor_groups', [ContractorGroupsController::class, 'store']);
            Route::get('/contractor_groups/{id}', [ContractorGroupsController::class, 'show']);
            Route::put('/contractor_groups/{id}', [ContractorGroupsController::class, 'update']);
            Route::delete('/contractor_groups/{id}', [ContractorGroupsController::class, 'destroy']);

            // == Product Groups ==
            Route::delete('/product_groups/bulk-delete', [ProductGroupsController::class, 'bulkDelete']);

            Route::get('/product_groups', [ProductGroupsController::class, 'index']);
            Route::post('/product_groups', [ProductGroupsController::class, 'store']);
            Route::get('/product_groups/{id}', [ProductGroupsController::class, 'show']);
            Route::put('/product_groups/{id}', [ProductGroupsController::class, 'update']);
            Route::delete('/product_groups/{id}', [ProductGroupsController::class, 'destroy']);

            Route::get('/bin', [BinController::class, 'index']);
            Route::delete('/bin', [BinController::class, 'delete']);
            Route::post('/bin', [BinController::class, 'recover']);

            Route::get('/view-settings', [UserViewSettingsController::class, 'index']);
            Route::delete('/view-settings/{id}', [UserViewSettingsController::class, 'destroy'])
                ->whereUuid('id');

            Route::post('/view-settings', [UserViewSettingsController::class, 'update']);

            Route::get('products/{id}/packings', [ProductPackingController::class, 'index'])
                ->whereUuid('id');
            Route::put('products/{id}/packings', [ProductPackingController::class, 'update'])
                ->whereUuid('id');

            // == Product ==
            Route::get('products', [ProductController::class, 'index']);
            Route::post('products', [ProductController::class, 'store']);
            Route::get('products/{id}', [ProductController::class, 'show'])
                ->whereUuid('id');
            Route::put('products/{id}', [ProductController::class, 'update'])
                ->whereUuid('id');
            Route::delete('products/{id}', [ProductController::class, 'destroy'])
                ->whereUuid('id');

            // == Product Attribute ==
            Route::get('product_attributes', [ProductAttributeController::class, 'index']);
            Route::get('product_attributes/getByProductId', [ProductAttributeController::class, 'getByProductId']);
            Route::post('product_attributes', [ProductAttributeController::class, 'store']);
            Route::get('product_attributes/{id}', [ProductAttributeController::class, 'show'])
                ->whereUuid('id');
            Route::put('product_attributes/{id}', [ProductAttributeController::class, 'update'])
                ->whereUuid('id');
            Route::delete('product_attributes/{id}', [ProductAttributeController::class, 'destroy'])
                ->whereUuid('id');

            Route::delete('products/deleteProductsCount/{id}', [ProductController::class, 'deleteProductsCount'])
                ->whereUuid('id');

            // == Barcode ==
            Route::post('barcode/bulk-store', [BarcodeController::class, 'bulkStore']);
            Route::put('barcode/bulk-update/{id}', [BarcodeController::class, 'bulkUpdate'])
                ->whereUuid('id');

            Route::get('barcode', [BarcodeController::class, 'index']);
            Route::post('barcode', [BarcodeController::class, 'store']);
            Route::get('barcode/{id}', [BarcodeController::class, 'show'])
                ->whereUuid('id');
            Route::put('barcode/{id}', [BarcodeController::class, 'update'])
                ->whereUuid('id');
            Route::delete('barcode/{id}', [BarcodeController::class, 'destroy'])
                ->whereUuid('id');
            Route::post('codeAndBarcodeGenerate', [BarcodeController::class, 'codeAndBarcodeGenerate']);
            Route::post('codeGenerate', [BarcodeController::class, 'codeGenerate']);
            Route::post('barcodeGenerateEAN13', [BarcodeController::class, 'barcodeGenerateEAN13']);
            Route::post('barcodeGenerateEAN8', [BarcodeController::class, 'barcodeGenerateEAN8']);
            Route::post('validateBarcodeEAN13', [BarcodeController::class, 'validateBarcodeEAN13']);
            Route::post('validateBarcodeEAN8', [BarcodeController::class, 'validateBarcodeEAN8']);
            Route::post('barcodeGenerateEAN13ForCabinet', [BarcodeController::class, 'barcodeGenerateEAN13ForCabinet']);
            Route::post('barcodeGenerateEAN8ForCabinet', [BarcodeController::class, 'barcodeGenerateEAN8ForCabinet']);
            Route::post('externalCodeGenerate', [BarcodeController::class, 'externalCodeGenerate']);

            // == Packing ==
            Route::get('packings', [PackingController::class, 'index']);
            Route::post('packings', [PackingController::class, 'store']);
            Route::get('packings/{id}', [PackingController::class, 'show'])
                ->whereUuid('id');
            Route::put('packings/{id}', [PackingController::class, 'update'])
                ->whereUuid('id');
            Route::delete('packings/{id}', [PackingController::class, 'destroy'])
                ->whereUuid('id');

            Route::delete('/contractors/bulk-delete', [ContractorController::class, 'bulkDelete']);
            Route::post('/contractors/bulk-copy', [ContractorController::class, 'bulkCopy']);
            Route::post('/contractors/archive', [ContractorController::class, 'archive']);
            Route::post('/contractors/unarchive', [ContractorController::class, 'unarchive']);

            // == ContractorEntity ==
            Route::get('contractors', [ContractorController::class, 'index']);
            Route::post('contractors', [ContractorController::class, 'store']);
            Route::get('contractors/{id}', [ContractorController::class, 'show'])
                ->whereUuid('id');
            Route::put('contractors/{id}', [ContractorController::class, 'update'])
                ->whereUuid('id');
            Route::delete('contractors/{id}', [ContractorController::class, 'destroy'])
                ->whereUuid('id');

            // == Cabinet Prices ==
            Route::get('/cabinets/prices', [CabinetPriceController::class, 'index']);
            Route::post('/cabinets/prices', [CabinetPriceController::class, 'store']);
            Route::get('/cabinets/prices/{id}', [CabinetPriceController::class, 'show'])
                ->whereUuid('id');
            Route::put('/cabinets/prices/{id}', [CabinetPriceController::class, 'update'])
                ->whereUuid('id');
            Route::delete('/cabinets/prices/{id}', [CabinetPriceController::class, 'destroy'])
                ->whereUuid('id');

            // == Cabinet ==
            Route::get('/cabinets', [CabinetController::class, 'index']);
            Route::get('/cabinets/{id}', [CabinetController::class, 'show'])
                ->whereUuid('id');
            Route::post('/cabinets', [CabinetController::class, 'store']);
            Route::put('/cabinets/{id}', [CabinetController::class, 'update'])
                ->whereUuid('id');
            Route::delete('/cabinets/{id}', [CabinetController::class, 'destroy'])
                ->whereUuid('id');

            // == Cabinet Settings ==
            Route::get('/cabinets/{cabinet}/settings', [CabinetSettingsController::class, 'show'])
                ->whereUuid('cabinet');
            Route::put('/cabinets/{cabinet}/settings', [CabinetSettingsController::class, 'update'])
                ->whereUuid('id');

            // == User cabinet_id
            Route::put('/users/cabinet/{id}', [UserCabinetController::class, 'update'])
                ->whereUuid('id');

            // == Export cabinet_id
            Route::get('/export1C/{id}', [Export1CController::class, 'export'])
                ->whereUuid('id');

            // == Excel cabinet_id
            Route::get('/excel/{id}', [ExportExcelController::class, 'index'])
                ->whereUuid('id');
            Route::get('/excel/download/{id}', [ExportExcelController::class, 'download'])
                ->whereUuid('id');
            Route::get('/excel/generateExcelContractors/{id}', [ExportExcelController::class, 'generateExcelContractors'])
                ->whereUuid('id');
            Route::get('/excel/generateExcelProducts/{id}', [ExportExcelController::class, 'generateExcelProducts'])
                ->whereUuid('id');
            Route::get('/excel/generatePdfProducts/{id}', [ExportExcelController::class, 'generatePdfProducts'])
                ->whereUuid('id');
            Route::delete('/excel/{id}', [ExportExcelController::class, 'destroy'])
                ->whereUuid('id');

            Route::post('/import-excel', [ExcelImportController::class, 'store'])->name('import-excel');

            Route::post('/v3/posting/fbs/list', [OzonV3PostingFbsListController::class, 'store'])->name('posting-fbs-list');

            // == Ozon ==
            Route::get('/ozon-credentials', [OzonCredentialsController::class, 'index']);
            Route::post('/ozon-credentials', [OzonCredentialsController::class, 'store']);
            Route::get('/ozon-credentials/{id}', [OzonCredentialsController::class, 'show'])->whereUuid('id');
            Route::put('/ozon-credentials/{id}', [OzonCredentialsController::class, 'update'])->whereUuid('id');
            Route::delete('/ozon-credentials/{id}', [OzonCredentialsController::class, 'destroy'])->whereUuid('id');

            Route::get('/v3/finance/transaction/list', [OzonV3FinanceTransactionListController::class, 'index']);
            Route::post('/v3/finance/transaction/list', [OzonV3FinanceTransactionListController::class, 'store']);

            Route::post('/v1/returns/list', [OzonV1ReturnsFboFbsListController::class, 'store']);
            Route::post('/v2/returns/rfbs/list', [OzonV2ReturnsRfbsListController::class, 'store']);

            Route::get('/v3/posting/fbs/list', [OzonV3PostingFbsListController::class, 'index']);
            Route::post('/v3/posting/fbs/list', [OzonV3PostingFbsListController::class, 'store']);
            Route::get('/v3/posting/fbs/list/{id}', [OzonV3PostingFbsListController::class, 'show'])->whereUuid('id');

            Route::get('/v1/warehouse/list', [OzonV1WarehouseController::class, 'index']);
            Route::post('/v1/warehouse/list', [OzonV1WarehouseController::class, 'store']);
            Route::get('/v1/warehouse/list/{id}', [OzonV1WarehouseController::class, 'show'])->whereUuid('id');
            Route::delete('/v1/warehouse/list/{id}', [OzonV1WarehouseController::class, 'destroy'])->whereUuid('id');

            // == Cabinet Groups ==
            Route::get('/groups/{id}', [GroupController::class, 'show'])
                ->whereUuid('id');
            Route::post('/groups', [GroupController::class, 'store']);
            Route::put('/groups/{id}', [GroupController::class, 'update'])
                ->whereUuid('id');

            // == Cabinet Brands ==
            Route::get('/brands', [BrandController::class, 'index']);
            Route::post('/brands', [BrandController::class, 'store']);
            Route::get('/brands/{id}', [BrandController::class, 'show'])
                ->whereUuid('id');
            Route::put('/brands/{id}', [BrandController::class, 'update'])
                ->whereUuid('id');
            Route::delete('/brands/{id}', [BrandController::class, 'destroy'])
                ->whereUuid('id');


            // == Cabinet Department ==
            Route::get('/departments', [DepartmentController::class, 'index']);
            Route::post('/departments', [DepartmentController::class, 'store']);
            Route::put('/departments/{id}', [DepartmentController::class, 'update'])
                ->whereUuid('id');
            Route::get('/departments/{id}', [DepartmentController::class, 'show'])
                ->whereUuid('id');
            Route::delete('/departments/{id}', [DepartmentController::class, 'destroy'])
                ->whereUuid('id');

            Route::get('/sales-channels/types', [SalesChannelController::class, 'getTypes']);


            Route::delete('/sales-channels/bulk-delete', [SalesChannelController::class, 'bulkDelete']);
            Route::post('/sales-channels/archive', [SalesChannelController::class, 'archive']);
            Route::post('/sales-channels/unarchive', [SalesChannelController::class, 'unarchive']);

            Route::get('/sales-channels', [SalesChannelController::class, 'index']);
            Route::get('/sales-channels/{id}', [SalesChannelController::class, 'show'])
                ->whereUuid('id');
            Route::post('/sales-channels', [SalesChannelController::class, 'store']);
            Route::put('/sales-channels/{id}', [SalesChannelController::class, 'update'])
                ->whereUuid('id');
            Route::delete('/sales-channels/{id}', [SalesChannelController::class, 'destroy'])
                ->whereUuid('id');

            // == Tax Rates (Налог на прибыль) ==
            Route::get('/tax_rates', [ProfitTaxRateController::class, 'index']);
            Route::get('/tax_rates/{id}', [ProfitTaxRateController::class, 'show'])
                ->whereUuid('id');
            Route::post('/tax_rates', [ProfitTaxRateController::class, 'store']);
            Route::put('/tax_rates/{id}', [ProfitTaxRateController::class, 'update'])
                ->whereUuid('id');
            Route::delete('/tax_rates/{id}', [ProfitTaxRateController::class, 'destroy'])
                ->whereUuid('id');

            Route::delete('/vat-rates/bulk-delete', [VatRateController::class, 'bulkDelete']);
            Route::post('/vat-rates/archive', [VatRateController::class, 'archive']);
            Route::post('/vat-rates/unarchive', [VatRateController::class, 'unarchive']);

            // == Vat Rates (Ставка НДС) ==
            Route::get('/vat-rates', [VatRateController::class, 'index']);
            Route::get('/vat-rates/{id}', [VatRateController::class, 'show'])
                ->whereUuid('id');
            Route::post('/vat-rates', [VatRateController::class, 'store']);
            Route::put('/vat-rates/{id}', [VatRateController::class, 'update'])
                ->whereUuid('id');
            Route::delete('/vat-rates/{id}', [VatRateController::class, 'destroy'])
                ->whereUuid('id');

            // == Cabinet Categiry ==
            Route::get('/cabinets/{cabinet}/categories', [ProductCategoryController::class, 'index'])
                ->whereUuid('cabinet');
            Route::get('/cabinets/{cabinet}/category/{id}', [ProductCategoryController::class, 'show'])
                ->whereUuid(['id','cabinet']);
            Route::post('/cabinets/{cabinet}/category', [ProductCategoryController::class, 'store']);
            Route::put('/cabinets/{cabinet}/category/{id}', [ProductCategoryController::class, 'update'])
                ->whereUuid(['id','cabinet']);
            Route::delete('/cabinets/{cabinet}/category/{id}', [ProductCategoryController::class, 'destroy'])
                ->whereUuid(['id','cabinet']);

            // == Attribute Groups ==
            Route::get('/attributes/groups', [AttributeGroupsController::class, 'index']);
            Route::post('/attributes/groups', [AttributeGroupsController::class, 'store']);
            Route::get('/attributes/groups/{id}', [AttributeGroupsController::class, 'show'])
                ->whereUuid('id');
            Route::put('/attributes/groups/{id}', [AttributeGroupsController::class, 'update'])
                ->whereUuid('id');
            Route::delete('/attributes/groups/{id}', [AttributeGroupsController::class, 'destroy'])
                ->whereUuid('id');

            // == Attribute ==
            Route::get('/attributes', [AttributeController::class, 'index']);
            Route::post('/attributes', [AttributeController::class, 'store']);
            Route::get('/attributes/{id}', [AttributeController::class, 'show'])
                ->whereUuid('id');
            Route::put('/attributes/{id}', [AttributeController::class, 'update'])
                ->whereUuid('id');
            Route::delete('/attributes/{id}', [AttributeController::class, 'destroy'])
                ->whereUuid('id');

            Route::get('/attribute_values', [AttributeValueController::class, 'index']);
            Route::post('/attribute_values', [AttributeValueController::class, 'store']);
            Route::get('/attribute_values/{id}', [AttributeValueController::class, 'show'])
                ->whereUuid('id');
            Route::put('/attribute_values/{id}', [AttributeValueController::class, 'update'])
                ->whereUuid('id');
            Route::delete('/attribute_values/{id}', [AttributeValueController::class, 'destroy'])
                ->whereUuid('id');

            Route::delete('/legals/bulk-delete', [LegalEntityController::class, 'bulkDelete']);
            Route::post('/legals/archive', [LegalEntityController::class, 'archive']);
            Route::post('/legals/unarchive', [LegalEntityController::class, 'unarchive']);
            // == Legal Entity ==
            Route::get('/legals', [LegalEntityController::class, 'index']);
            Route::get('/legals/{id}', [LegalEntityController::class, 'show'])
                ->whereUuid('id');
            Route::post('/legals', [LegalEntityController::class, 'store']);
            Route::put('/legals/{id}', [LegalEntityController::class, 'update'])
                ->whereUuid('id');
            Route::delete('/legals/{id}', [LegalEntityController::class, 'destroy'])
                ->whereUuid('id');

            Route::delete('/measurement-units/bulk-delete', [MeasurementUnitController::class, 'bulkDelete']);
            // == Meansurement Unit Groups (Группы единиц измерения) ==
            Route::get('/measurement-units/groups', [MeasurementUnitGroupController::class, 'index']);
            Route::get('/measurement-units/groups/{id}', [MeasurementUnitGroupController::class, 'show'])
                ->whereUuid('id');
            Route::post('/measurement-units/groups', [MeasurementUnitGroupController::class, 'store']);
            Route::put('/measurement-units/groups/{id}', [MeasurementUnitGroupController::class, 'update'])
                ->whereUuid('id');
            Route::delete('/measurement-units/groups/{id}', [MeasurementUnitGroupController::class, 'destroy'])
                ->whereUuid('id');

            // == Meansurement Unit (Единицы измерения) ==
            Route::get('/measurement-units', [MeasurementUnitController::class, 'index']);
            Route::get('/measurement-units/{id}', [MeasurementUnitController::class, 'show'])
                ->whereUuid('id');
            Route::post('/measurement-units', [MeasurementUnitController::class, 'store']);
            Route::put('/measurement-units/{id}', [MeasurementUnitController::class, 'update'])
                ->whereUuid('id');
            Route::delete('/measurement-units/{id}', [MeasurementUnitController::class, 'destroy'])
                ->whereUuid('id');

            // == Currencies (Валюты глобальные) ==
            Route::get('/currencies', [GlobalCurrencyController::class, 'index']);

            Route::get('/currencies/{id}', [GlobalCurrencyController::class, 'show'])
                ->whereUuid('id');

            Route::post('/cabinet-currencies/set-accouting', [CabinetCurrencyController::class, 'setAccouting']);
            Route::delete('/cabinet-currencies/bulk-delete', [CabinetCurrencyController::class, 'bulkDelete']);
            Route::post('/cabinet-currencies/archive', [CabinetCurrencyController::class, 'archive']);
            Route::post('/cabinet-currencies/unarchive', [CabinetCurrencyController::class, 'unarchive']);

            // == Currencies (Валюты кабинета) ==
            Route::get('/cabinet-currencies', [CabinetCurrencyController::class, 'index']);
            Route::get('/cabinet-currencies/{id}', [CabinetCurrencyController::class, 'show'])
                ->whereUuid('id');
            Route::post('/cabinet-currencies', [CabinetCurrencyController::class, 'store']);
            Route::put('/cabinet-currencies/{id}', [CabinetCurrencyController::class, 'update'])
                ->whereUuid('id');
            Route::delete('/cabinet-currencies/{id}', [CabinetCurrencyController::class, 'destroy'])
                ->whereUuid('id');

            // == Countries (Страны) ==
            Route::get('/countries', [CountryController::class, 'index']);
            Route::get('/countries/{id}', [CountryController::class, 'show'])
                ->whereUuid('id');
            Route::post('/countries', [CountryController::class, 'store']);
            Route::put('/countries/{id}', [CountryController::class, 'update'])
                ->whereUuid('id');
            Route::delete('/countries/{id}', [CountryController::class, 'destroy'])
                ->whereUuid('id');

            // == Discount (Скидки) ==
            Route::get('/discounts', [DiscountController::class, 'index']);
            Route::get('/discounts/{id}', [DiscountController::class, 'show'])
                ->whereUuid('id');
            Route::post('/discounts', [DiscountController::class, 'store']);
            Route::put('/discounts/{id}', [DiscountController::class, 'update'])
                ->whereUuid('id');
            Route::delete('/discounts/{id}', [DiscountController::class, 'destroy'])
                ->whereUuid('id');

            Route::get('/discounts/{id}/getSavings', [DiscountController::class, 'getSavings'])
                ->whereUuid('id');
            Route::get('/discounts/{id}/getProducts', [DiscountController::class, 'getProducts'])
                ->whereUuid('id');
            Route::get('/discounts/{id}/getGroups', [DiscountController::class, 'getGroups'])
                ->whereUuid('id');
            Route::put('/discounts/{id}/getStatus', [DiscountController::class, 'getStatus'])
                ->whereUuid('id');

            // == Warehouse Work Shedule ==
            Route::get('/work-schedules', [WarehouseWorkScheduleController::class, 'index']);
            Route::get('/work-schedules/{id}', [WarehouseWorkScheduleController::class, 'show'])
                ->whereUuid('id');
            Route::post('/work-schedules', [WarehouseWorkScheduleController::class, 'store']);
            Route::put('/work-schedules/{id}', [WarehouseWorkScheduleController::class, 'update'])
                ->whereUuid('id');
            Route::delete('/work-schedules/{id}', [WarehouseWorkScheduleController::class, 'destroy'])
                ->whereUuid('id');

            // == FBSWarehouses (Склады) ==
            Route::prefix('warehouses')->group(function (Router $router) {

                // == Warehouse Storage Areas (Зоны хранения) ==
                $router->get('/storage-areas', [WarehouseStorageAreaController::class, 'index']);
                $router->get('/storage-areas/{id}', [WarehouseStorageAreaController::class, 'show'])
                    ->whereUuid('id');
                $router->post('/storage-areas', [WarehouseStorageAreaController::class, 'store']);
                $router->put('/storage-areas/{id}', [WarehouseStorageAreaController::class, 'update'])
                    ->whereUuid('id');
                $router->delete('/storage-areas/{id}', [WarehouseStorageAreaController::class, 'destroy'])
                    ->whereUuid('id');

                // == Warehouse Cell Sizes (Типоразмеры ячеек) ==
                $router->get('/cells/sizes', [WarehouseCellSizeController::class, 'index']);
                $router->get('/cells/sizes/{id}', [WarehouseCellSizeController::class, 'show'])
                    ->whereUuid('id');
                $router->post('/cells/sizes', [WarehouseCellSizeController::class, 'store']);
                $router->put('/cells/sizes/{id}', [WarehouseCellSizeController::class, 'update'])
                    ->whereUuid('id');
                $router->delete('/cells/sizes/{id}', [WarehouseCellSizeController::class, 'destroy'])
                    ->whereUuid('id');

                // == Warehouse Cell Groups
                $router->get('/cells/groups', [WarehouseCellGroupController::class, 'index']);
                $router->get('/cells/groups/{id}', [WarehouseCellGroupController::class, 'show'])
                    ->whereUuid('id');
                $router->post('/cells/groups', [WarehouseCellGroupController::class, 'store']);
                $router->put('/cells/groups/{id}', [WarehouseCellGroupController::class, 'update'])
                    ->whereUuid('id');
                $router->delete('/cells/groups/{id}', [WarehouseCellGroupController::class, 'destroy'])
                    ->whereUuid('id');

                // == Warehouse Cells (Складские ячейки) ==
                $router->get('/cells', [WarehouseCellController::class, 'index']);
                $router->get('/cells/{id}', [WarehouseCellController::class, 'show'])
                    ->whereUuid('id');
                $router->post('/cells', [WarehouseCellController::class, 'store']);
                $router->put('/cells/{id}', [WarehouseCellController::class, 'update'])
                    ->whereUuid('id');
                $router->delete('/cells/{id}', [WarehouseCellController::class, 'destroy'])
                    ->whereUuid('id');

                // == Warehouse Groups
                $router->get('/groups', [WarehouseGroupController::class, 'index']);
                $router->get('/groups/{id}', [WarehouseGroupController::class, 'show'])
                    ->whereUuid('id');
                $router->post('/groups', [WarehouseGroupController::class, 'store']);
                $router->put('/groups/{id}', [WarehouseGroupController::class, 'update'])
                    ->whereUuid('id');
                $router->delete('/groups/{id}', [WarehouseGroupController::class, 'destroy'])
                    ->whereUuid('id');

                // == FBSWarehouses Contacts (Телефоны, адресса, записаная книга) ==
                $router->get('/phones', [WarehousePhoneController::class, 'index']);
                $router->get('/phones/{id}', [WarehousePhoneController::class, 'show'])
                    ->whereUuid('id');
                $router->post('/phones', [WarehousePhoneController::class, 'store']);
                $router->put('/phones/{id}', [WarehousePhoneController::class, 'update'])
                    ->whereUuid('id');
                $router->delete('/phones/{id}', [WarehousePhoneController::class, 'destroy'])
                    ->whereUuid('id');

                $router->get('/addresses', [WarehouseAddressController::class, 'index']);
                $router->get('/addresses/{id}', [WarehouseAddressController::class, 'show'])
                    ->whereUuid('id');
                $router->post('/addresses', [WarehouseAddressController::class, 'store']);
                $router->put('/addresses/{id}', [WarehouseAddressController::class, 'update'])
                    ->whereUuid('id');
                $router->delete('/addresses/{id}', [WarehouseAddressController::class, 'destroy'])
                    ->whereUuid('id');

                // == Order Scheme (Ордерная схема) ==
                $router->prefix('order-scheme')->group(function (Router $orderSchemeRouter) {

                    // Detection (Определение режима)
                    $orderSchemeRouter->get('/detection/is-active', [WarehouseOrderSchemeDetectionController::class, 'isActive']);

                    // Receipt Orders (Приходные ордера)
                    $orderSchemeRouter->get('/receipt-orders', [WarehouseReceiptOrdersController::class, 'index']);
                    $orderSchemeRouter->post('/receipt-orders', [WarehouseReceiptOrdersController::class, 'store']);
                    $orderSchemeRouter->get('/receipt-orders/{id}', [WarehouseReceiptOrdersController::class, 'show'])
                        ->whereUuid('id');
                    $orderSchemeRouter->put('/receipt-orders/{id}', [WarehouseReceiptOrdersController::class, 'update'])
                        ->whereUuid('id');
                    $orderSchemeRouter->delete('/receipt-orders/{id}', [WarehouseReceiptOrdersController::class, 'destroy'])
                        ->whereUuid('id');
                    $orderSchemeRouter->post('/receipt-orders/{id}/hold', [WarehouseReceiptOrdersController::class, 'hold'])
                        ->whereUuid('id');

                    // Issue Orders (Расходные ордера)
                    $orderSchemeRouter->get('/issue-orders', [WarehouseIssueOrdersController::class, 'index']);
                    $orderSchemeRouter->post('/issue-orders', [WarehouseIssueOrdersController::class, 'store']);
                    $orderSchemeRouter->get('/issue-orders/{id}', [WarehouseIssueOrdersController::class, 'show'])
                        ->whereUuid('id');
                    $orderSchemeRouter->put('/issue-orders/{id}', [WarehouseIssueOrdersController::class, 'update'])
                        ->whereUuid('id');
                    $orderSchemeRouter->delete('/issue-orders/{id}', [WarehouseIssueOrdersController::class, 'destroy'])
                        ->whereUuid('id');
                    $orderSchemeRouter->post('/issue-orders/{id}/hold', [WarehouseIssueOrdersController::class, 'hold'])
                        ->whereUuid('id');
                    $orderSchemeRouter->get('/issue-orders/write-off-reasons', [WarehouseIssueOrdersController::class, 'getWriteOffReasons']);

                    // Reservations (Резервирование)
                    $orderSchemeRouter->get('/reservations', [WarehouseReservationsController::class, 'index']);
                    $orderSchemeRouter->post('/reservations', [WarehouseReservationsController::class, 'store']);
                    $orderSchemeRouter->get('/reservations/{id}', [WarehouseReservationsController::class, 'show'])
                        ->whereUuid('id');
                    $orderSchemeRouter->post('/reservations/{id}/cancel', [WarehouseReservationsController::class, 'cancel'])
                        ->whereUuid('id');
                    $orderSchemeRouter->post('/reservations/mark-as-shipped', [WarehouseReservationsController::class, 'markAsShipped']);
                    $orderSchemeRouter->post('/reservations/release-expired', [WarehouseReservationsController::class, 'releaseExpired']);
                    $orderSchemeRouter->get('/reservations/availability', [WarehouseReservationsController::class, 'checkAvailability']);
                    $orderSchemeRouter->get('/reservations/types', [WarehouseReservationsController::class, 'getTypes']);

                    // Validation (Валидация)
                    $orderSchemeRouter->post('/validation/shipment', [WarehouseOrderSchemeValidationController::class, 'validateShipment']);

                    // Reports (Отчеты)
                    $orderSchemeRouter->get('/reports/stock-movement', [WarehouseOrderSchemeReportsController::class, 'stockMovementReport']);
                    $orderSchemeRouter->get('/reports/reservations', [WarehouseOrderSchemeReportsController::class, 'reservationReport']);
                    $orderSchemeRouter->get('/reports/analytics', [WarehouseOrderSchemeReportsController::class, 'orderSchemeAnalytics']);
                    $orderSchemeRouter->get('/reports/inventory', [WarehouseOrderSchemeReportsController::class, 'inventoryReport']);
                });

                $router->get('/', [WarehouseController::class, 'index']);
                $router->get('/{id}', [WarehouseController::class, 'show'])
                    ->whereUuid('id');
                $router->post('/', [WarehouseController::class, 'store']);
                $router->put('/{id}', [WarehouseController::class, 'update'])
                    ->whereUuid('id');
                $router->delete('/{id}', [WarehouseController::class, 'destroy'])
                    ->whereUuid('id');
            });
            // == Warehouse calendar ==
            Route::get('/warehouse-calendars/{id}', [WarehouseCalendarController::class, 'show'])
                ->whereUuid('id');


            // Cabinet Invites (Приглашения в кабинет)
            Route::get('/invites', [CabinetInviteController::class, 'index']);
            Route::get('/invites/received', [CabinetInviteController::class, 'received']);
            Route::get('/invites/{token}', [CabinetInviteController::class, 'show']);
            Route::post('/invites', [CabinetInviteController::class, 'store']);
            Route::delete('/invites/{id}', [CabinetInviteController::class, 'destroy'])
                ->whereUuid('id');

            Route::post('/invites/{id}', [CabinetInviteController::class, 'accept'])
                ->whereUuid('id');
            Route::post('/invites/{id}/decline', [CabinetInviteController::class, 'decline'])
                ->whereUuid('id');

            //Customer Order bulks
            Route::delete('/customer-orders/bulk-delete', [CustomerOrdersController::class, 'bulkDelete']);
            Route::patch('/customer-orders/bulk-held', [CustomerOrdersController::class, 'bulkHeld']);
            Route::patch('/customer-orders/bulk-unheld', [CustomerOrdersController::class, 'bulkUnheld']);
            Route::patch('/customer-orders/bulk-reserve', [CustomerOrdersController::class, 'bulkReserve']);
            Route::patch('/customer-orders/bulk-unreserve', [CustomerOrdersController::class, 'bulkUnreserve']);
            Route::post('/customer-orders/bulk-copy', [CustomerOrdersController::class, 'bulkCopy']);
            //Customer Order Items
            Route::get('/customer-orders/items', [CustomerOrderItemController::class, 'index']);
            Route::get('/customer-orders/items/{id}', [CustomerOrderItemController::class, 'show'])
                ->whereUuid('id');
            Route::post('/customer-orders/items', [CustomerOrderItemController::class, 'store']);
            Route::put('/customer-orders/items/{id}', [CustomerOrderItemController::class, 'update'])
                ->whereUuid('id');
            Route::delete('/customer-orders/items/{id}', [CustomerOrderItemController::class, 'destroy'])
                ->whereUuid('id');
            Route::get('/customer-orders/items/calculate-metrics', [CustomerOrderItemController::class, 'calculateMetrics']);

            //Customer Orders
            Route::get('/customer-orders', [CustomerOrdersController::class, 'index']);
            Route::get('/customer-orders/{id}', [CustomerOrdersController::class, 'show'])
                ->whereUuid('id');
            Route::post('/customer-orders', [CustomerOrdersController::class, 'store']);
            Route::put('/customer-orders/{id}', [CustomerOrdersController::class, 'update'])
                ->whereUuid('id');
            Route::delete('/customer-orders/{id}', [CustomerOrdersController::class, 'destroy'])
                ->whereUuid('id');

            //Vendor Order bulks
            Route::delete('/vendor-orders/bulk-delete', [VendorOrderController::class, 'bulkDelete']);
            Route::patch('/vendor-orders/bulk-held', [VendorOrderController::class, 'bulkHeld']);
            Route::patch('/vendor-orders/bulk-unheld', [VendorOrderController::class, 'bulkUnheld']);
            Route::patch('/vendor-orders/bulk-waiting', [VendorOrderController::class, 'bulkWaiting']);
            Route::patch('/vendor-orders/bulk-unwaiting', [VendorOrderController::class, 'bulkUnwaiting']);
            Route::post('/vendor-orders/bulk-copy', [VendorOrderController::class, 'bulkCopy']);

            // Vendor Order items
            Route::get('/vendor-orders/items', [VendorOrderItemController::class, 'index']);
            Route::get('/vendor-orders/items/{id}', [VendorOrderItemController::class, 'show'])
                ->whereUuid('id');
            Route::post('/vendor-orders/items/', [VendorOrderItemController::class, 'store']);
            Route::put('/vendor-orders/items/{id}', [VendorOrderItemController::class, 'update'])
                ->whereUuid('id');
            Route::delete('/vendor-orders/items/{id}', [VendorOrderItemController::class, 'destroy'])
                ->whereUuid('id');
            Route::get('/vendor-orders/items/calculate-metrics', [VendorOrderItemController::class, 'calculateMetrics']);
            // Vendor Orders
            Route::get('/vendor-orders', [VendorOrderController::class, 'index']);
            Route::get('/vendor-orders/{id}', [VendorOrderController::class, 'show'])
                ->whereUuid('id');
            Route::post('/vendor-orders', [VendorOrderController::class, 'store']);
            Route::put('/vendor-orders/{id}', [VendorOrderController::class, 'update'])
                ->whereUuid('id');
            Route::delete('/vendor-orders/{id}', [VendorOrderController::class, 'destroy'])
                ->whereUuid('id');

            Route::delete('/acceptances/bulk-delete', [AcceptanceController::class, 'bulkDelete']);
            Route::post('/acceptances/bulk-copy', [AcceptanceController::class, 'bulkCopy']);
            Route::patch('/acceptances/bulk-held', [AcceptanceController::class, 'bulkHeld']);
            Route::patch('/acceptances/bulk-unheld', [AcceptanceController::class, 'bulkUnheld']);
            //Shipment items
            Route::get('/acceptances/items', [AcceptanceItemController::class, 'index']);
            Route::get('/acceptances/items/{id}', [AcceptanceItemController::class, 'show'])
                ->whereUuid('id');
            Route::post('/acceptances/items', [AcceptanceItemController::class, 'store']);
            Route::put('/acceptances/items/{id}', [AcceptanceItemController::class, 'update'])
                ->whereUuid('id');
            Route::delete('/acceptances/items/{id}', [AcceptanceItemController::class, 'destroy'])
                ->whereUuid('id');
            Route::get('/acceptances/items/calculate-metrics', [AcceptanceItemController::class, 'calculateMetrics']);

            Route::get('/acceptances', [AcceptanceController::class, 'index']);
            Route::get('/acceptances/{id}', [AcceptanceController::class, 'show'])->whereUuid('id');
            Route::post('/acceptances', [AcceptanceController::class, 'store']);
            Route::put('/acceptances/{id}', [AcceptanceController::class, 'update'])
                ->whereUuid('id');
            Route::delete('/acceptances/{id}', [AcceptanceController::class, 'destroy'])
                ->whereUuid('id');

            Route::delete('/shipments/bulk-delete', [ShipmentController::class, 'bulkDelete']);
            Route::post('/shipments/bulk-copy', [ShipmentController::class, 'bulkCopy']);
            Route::patch('/shipments/bulk-held', [ShipmentController::class, 'bulkHeld']);
            Route::patch('/shipments/bulk-unheld', [ShipmentController::class, 'bulkUnheld']);

            //Shipment items
            Route::get('/shipments/items', [ShipmentItemController::class, 'index']);
            Route::get('/shipments/items/{id}', [ShipmentItemController::class, 'show'])
                ->whereUuid('id');
            Route::post('/shipments/items', [ShipmentItemController::class, 'store']);
            Route::put('/shipments/items/{id}', [ShipmentItemController::class, 'update'])
                ->whereUuid('id');
            Route::delete('/shipments/items/{id}', [ShipmentItemController::class, 'destroy'])
                ->whereUuid('id');
            Route::get('/shipments/items/calculate-metrics', [ShipmentItemController::class, 'calculateMetrics']);

            Route::get('/shipments', [ShipmentController::class, 'index']);
            Route::get('/shipments/{id}', [ShipmentController::class, 'show'])
                ->whereUuid('id');
            Route::post('/shipments', [ShipmentController::class, 'store']);
            Route::put('/shipments/{id}', [ShipmentController::class, 'update'])
                ->whereUuid('id');
            Route::delete('/shipments/{id}', [ShipmentController::class, 'destroy'])
                ->whereUuid('id');

            Route::get('/incoming-payments/items', [IncomingPaymentItemController::class, 'index']);
            Route::get('/incoming-payments/items/{id}', [IncomingPaymentItemController::class, 'show'])
                ->whereUuid('id');
            Route::post('/incoming-payments/items', [IncomingPaymentItemController::class, 'store']);
            Route::put('/incoming-payments/items/{id}', [IncomingPaymentItemController::class, 'update'])
                ->whereUuid('id');
            Route::delete('/incoming-payments/items/{id}', [IncomingPaymentItemController::class, 'destroy'])
                ->whereUuid('id');

            Route::get('/incoming-payments', [IncomingPaymentController::class, 'index']);
            Route::get('/incoming-payments/{id}', [IncomingPaymentController::class, 'show'])
                ->whereUuid('id');
            Route::post('/incoming-payments', [IncomingPaymentController::class, 'store']);
            Route::put('/incoming-payments/{id}', [IncomingPaymentController::class, 'update'])
                ->whereUuid('id');
            Route::delete('/incoming-payments/{id}', [IncomingPaymentController::class, 'destroy'])
                ->whereUuid('id');

            Route::get('/outgoing-payments/items', [OutgoingPaymentItemController::class, 'index']);
            Route::get('/outgoing-payments/items/{id}', [OutgoingPaymentItemController::class, 'show'])
                ->whereUuid('id');
            Route::post('/outgoing-payments/items', [OutgoingPaymentItemController::class, 'store']);
            Route::put('/outgoing-payments/items/{id}', [OutgoingPaymentItemController::class, 'update'])
                ->whereUuid('id');
            Route::delete('/outgoing-payments/items/{id}', [OutgoingPaymentItemController::class, 'destroy'])
                ->whereUuid('id');

            Route::get('/outgoing-payments', [OutgoingPaymentController::class, 'index']);
            Route::get('/outgoing-payments/{id}', [OutgoingPaymentController::class, 'show'])
                ->whereUuid('id');
            Route::post('/outgoing-payments', [OutgoingPaymentController::class, 'store']);
            Route::put('/outgoing-payments/{id}', [OutgoingPaymentController::class, 'update'])
                ->whereUuid('id');
            Route::delete('/outgoing-payments/{id}', [OutgoingPaymentController::class, 'destroy'])
                ->whereUuid('id');


            Route::delete('/payments/bulk-delete', [FinanceController::class, 'bulkDelete']);
            Route::post('/payments/bulk-copy', [FinanceController::class, 'bulkCopy']);
            Route::patch('/payments/bulk-held', [FinanceController::class, 'bulkHeld']);
            Route::patch('/payments/bulk-unheld', [FinanceController::class, 'bulkUnheld']);

            Route::get('/payments', [FinanceController::class, 'index']);

            Route::get('/roles', [RoleController::class, 'index']);
            Route::get('/roles/{id}', [RoleController::class, 'show'])
                ->whereUuid('id');
            Route::post('/roles', [RoleController::class, 'store']);
            Route::put('/roles/{id}', [RoleController::class, 'update'])
                ->whereUuid('id');
            Route::delete('/roles/{id}', [RoleController::class, 'destroy'])
                ->whereUuid('id');

            Route::get('/permissions', [PermissionController::class, 'index']);

            Route::delete('/employees/bulk-delete', [EmployeeController::class, 'bulkDelete']);
            Route::post('/employees/archive', [EmployeeController::class, 'archive']);
            Route::post('/employees/unarchive', [EmployeeController::class, 'unarchive']);

            Route::get('/employees/{cabinet_id}/profile', [EmployeeController::class, 'profile'])
                ->whereUuid('cabinet_id');

            Route::get('/employees/{id}/permissions', [EmployeePermissionController::class, 'show'])
                ->whereUuid('id');
            Route::put('/employees/{id}/permissions', [EmployeePermissionController::class, 'update'])
                ->whereUuid('id');

            Route::get('/employees', [EmployeeController::class, 'index']);
            Route::get('/employees/{id}', [EmployeeController::class, 'show'])
                ->whereUuid('id');
            Route::put('/employees/{id}', [EmployeeController::class, 'update'])
                ->whereUuid('id');
            Route::delete('/employees/{id}', [EmployeeController::class, 'destroy'])
                ->whereUuid('id');

            Route::post('/departments/permissions', [DepartamentPermissionController::class, 'store']);
            Route::get('/departments/{id}/permissions', [DepartamentPermissionController::class, 'show'])
                ->whereUuid('id');
            Route::put('/departments/{id}/permissions', [DepartamentPermissionController::class, 'update'])
                ->whereUuid('id');

            Route::get('/related_documents', [RelatedDocumentController::class, 'index']);
            Route::get('/related_documents/{id}', [RelatedDocumentController::class, 'show'])
                ->whereUuid('id');
            Route::post('/related_documents', [RelatedDocumentController::class, 'store']);

            // == Statuses ==
            Route::get('/statuses', [StatusController::class, 'index']);
            Route::post('/statuses', [StatusController::class, 'store']);
            Route::get('/statuses/{id}', [StatusController::class, 'show'])
                ->whereUuid('id');
            Route::put('/statuses/{id}', [StatusController::class, 'update'])
                ->whereUuid('id');
            Route::delete('/statuses/{id}', [StatusController::class, 'destroy'])
                ->whereUuid('id');


            Route::get('/files', [FilesController::class, 'index']);
            Route::post('/files', [FilesController::class, 'store']);
            Route::get('/files/{id}', [FilesController::class, 'show'])
                ->whereUuid('id');
            Route::get('/files/{id}/download', [FilesController::class, 'download'])
                ->whereUuid('id');
            Route::delete('/files/{id}', [FilesController::class, 'destroy'])
                ->whereUuid('id');

            // == Bookmarks ==
            Route::get('/bookmarks', [BookmarkController::class, 'index']);
            Route::post('/bookmarks', [BookmarkController::class, 'store']);
            Route::get('/bookmarks/{id}', [BookmarkController::class, 'show'])
                ->whereUuid('id');
            Route::put('/bookmarks/{id}', [BookmarkController::class, 'update'])
                ->whereUuid('id');
            Route::delete('/bookmarks/{id}', [BookmarkController::class, 'destroy'])
                ->whereUuid('id');

            Route::get('/contracts', [ContractController::class, 'index']);
            Route::get('/contracts/{id}', [ContractController::class, 'show']);
            Route::post('/contracts', [ContractController::class, 'store']);
            Route::put('/contracts/{id}', [ContractController::class, 'update']);
            Route::delete('/contracts/{id}', [ContractController::class, 'destroy']);

            Route::get('/comission-reports', [ContractController::class, 'index']);
            Route::get('/comission-reports/{id}', [ContractController::class, 'show']);
            Route::post('/comission-reports', [ContractController::class, 'store']);
            Route::put('/comission-reports/{id}', [ContractController::class, 'update']);
            Route::delete('/comission-reports/{id}', [ContractController::class, 'destroy']);

            Route::get('/comission-reports/received', [ReceivedComissionReportsController::class, 'index']);
            Route::get('/comission-reports/received/{id}', [ReceivedComissionReportsController::class, 'show']);
            Route::post('/comission-reports/received', [ReceivedComissionReportsController::class, 'store']);
            Route::put('/comission-reports/received/{id}', [ReceivedComissionReportsController::class, 'update']);
            Route::delete('/comission-reports/received/{id}', [ReceivedComissionReportsController::class, 'destroy']);

            Route::get('/comission-reports/issued', [IssuedComissionReportsController::class, 'index']);
            Route::get('/comission-reports/issued/{id}', [IssuedComissionReportsController::class, 'show']);
            Route::post('/comission-reports/issued', [IssuedComissionReportsController::class, 'store']);
            Route::put('/comission-reports/issued/{id}', [IssuedComissionReportsController::class, 'update']);
            Route::delete('/comission-reports/issued/{id}', [IssuedComissionReportsController::class, 'destroy']);

            Route::get('/comission-reports/received/{id}/items/realized', [ReceivedComissionReportsRealizedItemsController::class, 'index']);
            Route::get('/comission-reports/received/items/realized/{id}', [ReceivedComissionReportsRealizedItemsController::class, 'show']);
            Route::post('/comission-reports/received/items/realized', [ReceivedComissionReportsRealizedItemsController::class, 'store']);
            Route::put('/comission-reports/received/items/realized/{id}', [ReceivedComissionReportsRealizedItemsController::class, 'update']);
            Route::delete('/comission-reports/received/items/realized/{id}', [ReceivedComissionReportsRealizedItemsController::class, 'destroy']);

            Route::get('/comission-reports/received/{id}/items/returned', [ReceivedComissionReportsReturnItemsController::class, 'index']);
            Route::get('/comission-reports/received/items/returned/{item_id}', [ReceivedComissionReportsReturnItemsController::class, 'show']);
            Route::post('/comission-reports/received/items/returned', [ReceivedComissionReportsReturnItemsController::class, 'store']);
            Route::put('/comission-reports/received/items/returned/{id}', [ReceivedComissionReportsReturnItemsController::class, 'update']);
            Route::delete('/comission-reports/received/items/returned/{item_id}', [ReceivedComissionReportsReturnItemsController::class, 'destroy']);

            Route::get('/comission-reports/issued/{id}/items', [IssuedComissionReportsItemsController::class, 'index']);
            Route::get('/comission-reports/issued/items/{id}', [IssuedComissionReportsItemsController::class, 'show']);
            Route::post('/comission-reports/issued/items', [IssuedComissionReportsItemsController::class, 'store']);
            Route::put('/comission-reports/issued/items/{id}', [IssuedComissionReportsItemsController::class, 'update']);
            Route::delete('/comission-reports/issued/items/{id}', [IssuedComissionReportsItemsController::class, 'destroy']);

            Route::get('/goods-transfers/items', [GoodsTransferItemController::class, 'index']);
            Route::get('/goods-transfers/items/{id}', [GoodsTransferItemController::class, 'show'])
                ->whereUuid('id');
            Route::post('/goods-transfers/items', [GoodsTransferItemController::class, 'store']);
            Route::put('/goods-transfers/items/{id}', [GoodsTransferItemController::class, 'update'])
                ->whereUuid('id');
            Route::delete('/goods-transfers/items/{id}', [GoodsTransferItemController::class, 'destroy'])
                ->whereUuid('id');

            Route::get('/goods-transfers', [GoodsTransferController::class, 'index']);
            Route::get('/goods-transfers/{id}', [GoodsTransferController::class, 'show'])
                ->whereUuid('id');
            Route::post('/goods-transfers', [GoodsTransferController::class, 'store']);
            Route::put('/goods-transfers/{id}', [GoodsTransferController::class, 'update'])
                ->whereUuid('id');
            Route::delete('/goods-transfers/{id}', [GoodsTransferController::class, 'destroy'])
                ->whereUuid('id');
        });

        Route::middleware('throttle:50,60')->group(static function () {
            Route::get('/suggest/find-party', [SuggestController::class, 'findParty']);
            Route::get('/suggest/bank', [SuggestController::class, 'bank']);
            Route::get('/suggest/address', [SuggestController::class, 'address']);
        });

        // == User ==
        Route::get('profile', [UserAuthController::class, 'profile']);
        Route::get('logout', [UserAuthController::class, 'logout']);
        Route::delete('destroy', [UserAuthController::class, 'destroy']);

        Route::get('/user_settings', [UserSettingsController::class, 'index']);
        Route::put('/user-settings/{userId}/discounts', [UserSettingsController::class, 'discount']);
        Route::put('/user_settings', [UserSettingsController::class, 'update']);

        // == Product Siz Средства индивидуальной защиты ==
        Route::get('/product/siz', [ProductSizController::class, 'index']);
    });

Route::fallback(static function () {
    abort(404, 'API resource not found');
});
