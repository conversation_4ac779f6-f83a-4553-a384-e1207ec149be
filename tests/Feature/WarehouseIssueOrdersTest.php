<?php

namespace Tests\Feature;

use App\Contracts\Services\Internal\WarehouseIssueOrdersServiceContract;
use App\Models\Cabinet;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Product;
use App\Models\User;
use App\Models\VatRate;
use App\Models\Warehouse;
use App\Models\WarehouseIssueOrder;
use App\Models\WarehouseIssueOrderItem;
use App\Models\WarehouseItem;
use App\Models\WarehouseOrderScheme;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class WarehouseIssueOrdersTest extends TestCase
{
    use WithFaker;
    use RefreshDatabase;

    protected $issueOrderService;
    protected $employee;
    protected $cabinet;
    protected $otherCabinet;
    protected $department;
    protected $warehouse;
    protected $product;
    protected $vatRate;
    protected $warehouseItem;

    /**
     * @throws BindingResolutionException
     */
    protected function setUp(): void
    {
        parent::setUp();
        $this->refreshApplication();

        Queue::fake();

        $this->issueOrderService = $this->app->make(WarehouseIssueOrdersServiceContract::class);

        // Создаем и аутентифицируем пользователя
        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $user->id,
        ]);
        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->otherCabinet = Cabinet::factory()->create();
        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        // Создаем склад с ордерной схемой
        $this->warehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        // Создаем настройки ордерной схемы отгрузок
        WarehouseOrderScheme::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'on_shipment_from' => '2024-01-01',
        ]);

        $this->product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $this->vatRate = VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        // Создаем товар на складе для списания
        $this->warehouseItem = WarehouseItem::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'quantity' => 100,
            'available_quantity' => 100,
            'quality_status' => 'good',
        ]);
    }

    public function test_can_get_issue_orders_list(): void
    {
        // Arrange
        $cabinetId = $this->cabinet->id;
        $otherCabinetId = $this->otherCabinet->id;

        // Создаем расходные ордера для нашего кабинета
        WarehouseIssueOrder::factory()->count(3)->create([
            'cabinet_id' => $cabinetId,
            'warehouse_id' => $this->warehouse->id,
        ]);

        // Создаем расходные ордера для другого кабинета
        WarehouseIssueOrder::factory()->count(2)->create([
            'cabinet_id' => $otherCabinetId
        ]);

        $filters = [
            'page' => 1,
            'per_page' => 15,
            'sortField' => 'created_at',
            'sortDirection' => 'desc',
            'cabinet_id' => $cabinetId
        ];

        // Act
        $response = $this->getJson('/api/internal/warehouses/order-scheme/issue-orders?' . http_build_query($filters));

        // Assert
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data' => [
                '*' => [
                    'id',
                    'cabinet_id',
                    'warehouse_id',
                    'employee_id',
                    'department_id',
                    'number',
                    'date_from',
                    'write_off_reason',
                    'held',
                    'total_quantity',
                    'total_cost',
                ]
            ],
            'meta'
        ]);

        $responseData = $response->json();
        $this->assertCount(3, $responseData['data']);
    }

    public function test_can_create_issue_order(): void
    {
        // Arrange
        $issueOrderData = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'number' => 'РО-001',
            'date_from' => '2024-01-15',
            'write_off_reason' => 'defective',
            'reason_description' => 'Обнаружен брак при проверке',
            'total_quantity' => 10,
            'total_cost' => '1500.00',
            'comment' => 'Тестовый расходный ордер',
            'items' => [
                [
                    'product_id' => $this->product->id,
                    'warehouse_item_id' => $this->warehouseItem->id,
                    'quantity' => 10,
                    'unit_price' => '150.00',
                    'total_price' => '1500.00',
                    'batch_number' => 'BATCH001',
                    'lot_number' => 'LOT001',
                    'expiry_date' => '2024-12-31',
                    'vat_rate_id' => $this->vatRate->id,
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/order-scheme/issue-orders', $issueOrderData);

        // Assert
        $response->assertStatus(201);
        $response->assertJsonStructure([
            'id',
            'message'
        ]);

        $this->assertDatabaseHas('warehouse_issue_orders', [
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'number' => 'РО-001',
            'write_off_reason' => 'defective',
            'total_quantity' => 10,
            'total_cost' => '1500.00',
        ]);

        $issueOrder = WarehouseIssueOrder::where('number', 'РО-001')->first();
        $this->assertDatabaseHas('warehouse_issue_order_items', [
            'issue_order_id' => $issueOrder->id,
            'product_id' => $this->product->id,
            'warehouse_item_id' => $this->warehouseItem->id,
            'quantity' => 10,
        ]);
    }

    public function test_can_show_issue_order(): void
    {
        // Arrange
        $issueOrder = WarehouseIssueOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
        ]);

        // Act
        $response = $this->getJson("/api/internal/warehouses/order-scheme/issue-orders/{$issueOrder->id}");

        // Assert
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'id',
            'cabinet_id',
            'warehouse_id',
            'employee_id',
            'department_id',
            'number',
            'date_from',
            'write_off_reason',
            'held',
            'total_quantity',
            'total_cost',
        ]);
    }

    public function test_can_update_issue_order(): void
    {
        // Arrange
        $issueOrder = WarehouseIssueOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'held' => false,
        ]);

        $updateData = [
            'number' => 'РО-UPDATED',
            'write_off_reason' => 'expired',
            'reason_description' => 'Истек срок годности',
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouses/order-scheme/issue-orders/{$issueOrder->id}", $updateData);

        // Assert
        $response->assertStatus(200);
        $response->assertJson([
            'message' => 'Расходный ордер обновлен'
        ]);

        $this->assertDatabaseHas('warehouse_issue_orders', [
            'id' => $issueOrder->id,
            'number' => 'РО-UPDATED',
            'write_off_reason' => 'expired',
            'reason_description' => 'Истек срок годности',
        ]);
    }

    public function test_can_delete_issue_order(): void
    {
        // Arrange
        $issueOrder = WarehouseIssueOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'held' => false,
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/warehouses/order-scheme/issue-orders/{$issueOrder->id}");

        // Assert
        $response->assertStatus(200);
        $response->assertJson([
            'message' => 'Расходный ордер удален'
        ]);

        $this->assertDatabaseMissing('warehouse_issue_orders', [
            'id' => $issueOrder->id,
        ]);
    }

    public function test_can_hold_issue_order(): void
    {
        // Arrange
        $issueOrder = WarehouseIssueOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'held' => false,
        ]);

        // Act
        $response = $this->postJson("/api/internal/warehouses/order-scheme/issue-orders/{$issueOrder->id}/hold");

        // Assert
        $response->assertStatus(200);
        $response->assertJson([
            'message' => 'Расходный ордер проведен'
        ]);

        $this->assertDatabaseHas('warehouse_issue_orders', [
            'id' => $issueOrder->id,
            'held' => true,
        ]);
    }

    public function test_can_get_write_off_reasons(): void
    {
        // Act
        $response = $this->getJson('/api/internal/warehouses/order-scheme/issue-orders/write-off-reasons');

        // Assert
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'reasons' => [
                'defective',
                'expired',
                'shortage',
                'internal_use',
                'return_to_supplier',
                'damage',
                'other'
            ]
        ]);
    }

    public function test_cannot_update_held_issue_order(): void
    {
        // Arrange
        $issueOrder = WarehouseIssueOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'held' => true,
        ]);

        $updateData = [
            'number' => 'РО-UPDATED',
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouses/order-scheme/issue-orders/{$issueOrder->id}", $updateData);

        // Assert
        $response->assertStatus(422);
    }

    public function test_cannot_delete_held_issue_order(): void
    {
        // Arrange
        $issueOrder = WarehouseIssueOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'held' => true,
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/warehouses/order-scheme/issue-orders/{$issueOrder->id}");

        // Assert
        $response->assertStatus(422);
    }

    public function test_validates_order_scheme_for_warehouse(): void
    {
        // Arrange - создаем склад без ордерной схемы отгрузок
        $regularWarehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $issueOrderData = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $regularWarehouse->id,
            'date_from' => '2024-01-15',
            'write_off_reason' => 'defective',
            'total_quantity' => 10,
            'total_cost' => '1500.00',
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/order-scheme/issue-orders', $issueOrderData);

        // Assert
        $response->assertStatus(422);
    }
}
